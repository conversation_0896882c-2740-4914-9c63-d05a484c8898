using AutoMapper;
using MediatR;
using Microsoft.AspNetCore.Http;
using Orion.SharedKernel.Application.Common.Mapping;
using Orion.SharedKernel.Application.Exceptions;
using Reports.Application.Common.Constants;
using Reports.Application.Common.Helpers;
using Reports.Application.DTOs.Reports.MassBalance;
using Reports.Application.Features.Web;
using Reports.Domain;
using Reports.Domain.Common;
using Reports.Domain.Entities;
using Reports.Domain.ValueObjects;
using Shared.Application.Common.Services;
using Shared.Domain.Constants;

namespace Reports.Application.Features.Web.Queries.Reports.MassBalance;

/// <summary>
/// Handler for exporting Mass Balance reports to Excel format
/// </summary>
internal class ExportMassBalanceHandler : MappingService, IRequestHandler<ExportMassBalanceRequest, ExportMassBalanceResponse>
{
    private readonly IReportsUnitOfWork _unitOfWork;
    private readonly IFileExporterService _fileExporterService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private const int MinutesTillCacheInvalidation = 360;
    private Dictionary<string, string> _areaCodeMappings = new();

    public ExportMassBalanceHandler(
        IMapper mapper,
        IReportsUnitOfWork unitOfWork,
        IFileExporterService fileExporterService,
        IHttpContextAccessor httpContextAccessor) : base(mapper)
    {
        _unitOfWork = unitOfWork;
        _fileExporterService = fileExporterService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<ExportMassBalanceResponse> Handle(ExportMassBalanceRequest request, CancellationToken cancellationToken)
    {
        // Get the Mass Balance data using the same logic as GetMassBalanceHandler
        var massBalanceData = await GetMassBalanceData(request, cancellationToken);
        
        // Transform to export DTO
        var exportData = TransformToExportDto(massBalanceData, request);
        
        // Generate Excel file
        var excelBytes = MassBalanceExcelHelper.GenerateExcel(exportData);
        
        // Determine format and create response
        var format = GetExcelFormat(request.ExcelFormat);
        var contentType = GetContentType(format);
        var fileExtension = format.ToString().ToLower();
        var fileName = GenerateFileName(request.FromDate, fileExtension);
        
        var result = Results.File(
            fileContents: excelBytes,
            contentType: contentType,
            fileDownloadName: fileName);

        return new ExportMassBalanceResponse
        {
            TotalRecords = exportData.BalanceSheetRows.Count + exportData.DistributionRows.Count,
            Result = result
        };
    }

    private async Task<Domain.ValueObjects.MassBalance> GetMassBalanceData(ExportMassBalanceRequest request, CancellationToken cancellationToken)
    {
        // Reuse the same data retrieval logic from GetMassBalanceHandler
        var weighinDistributions = await GetDistributions(request, cancellationToken);
        var f14 = (IReadOnlyList<ReportFormat14>)await GetReportFormat14(request, cancellationToken);
        var f34 = (IReadOnlyList<ReportFormat34>)await GetReportFormat34(request, cancellationToken);
        var weighins = (IReadOnlyList<WeighingScale>)await GetWeighins(request, cancellationToken);
        var recyclingAreas = (IReadOnlyList<RecyclingArea>)await GetRecyclingAreas(cancellationToken);

        // Build area code mappings from database data
        _areaCodeMappings = recyclingAreas.ToDictionary(
            area => area.Name,
            area => area.Code.ToString(),
            StringComparer.OrdinalIgnoreCase);

        return Domain.ValueObjects.MassBalance.Create()
            .ProcessDistributions(weighinDistributions)
            .ProcessWeighins(weighins)
            .ProcessRecollectionAndTransport(f14)
            .ProcessFinalDisposition(f34, recyclingAreas)
            .Validate();
    }

    private MassBalanceExportDto TransformToExportDto(Domain.ValueObjects.MassBalance massBalance, ExportMassBalanceRequest request)
    {
        var period = $"{request.FromDate:yyyy/MM}";
        
        return new MassBalanceExportDto
        {
            Title = MassBalanceExcelConstants.MainTitle,
            GenerationDate = DateTime.Now,
            Period = period,
            BalanceSheetRows = CreateBalanceSheetRows(massBalance),
            FinalDispositionSummary = CreateFinalDispositionSummary(massBalance),
            DistributionRows = CreateDistributionRows(massBalance)
        };
    }

    private List<BalanceSheetRowDto> CreateBalanceSheetRows(Domain.ValueObjects.MassBalance massBalance)
    {
        var rows = new List<BalanceSheetRowDto>();
        
        // Combine F14 (RecollectionAndTransport) and F34 (FinalDisposition) data by area
        var recollectionAreas = massBalance.RecollectionAndTransport.PerArea.ToDictionary(x => x.RecyclingArea, x => x.Resume);
        var finalDispositionAreas = massBalance.FinalDisposition.PerArea.ToDictionary(x => x.RecyclingArea, x => x.Resume);
        
        // Get all unique areas
        var allAreas = recollectionAreas.Keys.Union(finalDispositionAreas.Keys).OrderBy(x => x != "Medellín").ThenBy(x => x);
        
        foreach (var area in allAreas)
        {
            var recollection = recollectionAreas.GetValueOrDefault(area);
            var finalDisposition = finalDispositionAreas.GetValueOrDefault(area);
            
            rows.Add(new BalanceSheetRowDto
            {
                AreaCode = GetAreaCode(area),
                AreaName = area,
                UrbanCleaningTons = recollection?.UrbanCleaning ?? 0,
                SweepingTons = recollection?.Sweeping ?? 0,
                NonRecyclableTons = recollection?.NonRecyclable ?? 0,
                RejectionTons = recollection?.Rejection ?? 0,
                RecyclableTons = recollection?.Recyclable ?? 0,
                TotalByNUAP = finalDisposition?.Total ?? 0,
                Discounts = finalDisposition?.Discount ?? 0
            });
        }
        
        return rows;
    }

    private FinalDispositionSummaryDto CreateFinalDispositionSummary(Domain.ValueObjects.MassBalance massBalance)
    {
        return new FinalDispositionSummaryDto
        {
            WeighingEmvariasTons = massBalance.Weighins.Emvarias,
            WeighingTotalTons = massBalance.Weighins.Total,
            FinalDispositionEmvariasTons = massBalance.FinalDisposition.Totals.Emvarias,
            FinalDispositionTotalTons = massBalance.FinalDisposition.Totals.Total,
            FinalDispositionDiscountTons = massBalance.FinalDisposition.Totals.Discount
        };
    }

    private List<DistributionRowDto> CreateDistributionRows(Domain.ValueObjects.MassBalance massBalance)
    {
        return massBalance.Distributions.Select(d => new DistributionRowDto
        {
            RecyclingArea = d.RecyclingArea,
            ReportedTons = d.ReportedTons,
            Trips = d.Trips,
            CalculatedDistributedTons = d.DistributedTons,
            CalculatedTotalTons = d.TotalTons,
            CalculatedDeviationTons = d.DeviationTons,
            TollSharedRouteTons = d.TollSharedRouteTons,
            CalculatedDistributionTollPercentage = d.DistributionTollPercentage,
            Compensation = d.DistributedTons * d.DistributionTollPercentage // Calculate compensation
        }).ToList();
    }

    private string GetAreaCode(string areaName)
    {
        // Get area code from database mappings
        if (_areaCodeMappings.TryGetValue(areaName, out var code))
        {
            return code;
        }

        // Default value for unknown areas
        return "000000000";
    }

    private ExcelFormat GetExcelFormat(string? format)
    {
        if (string.IsNullOrEmpty(format))
        {
            // Try to get from headers
            format = _httpContextAccessor.HttpContext?.Request.Headers["X-ExcelFormat"];
        }

        if (Enum.TryParse<ExcelFormat>(format, ignoreCase: true, out var excelFormat))
        {
            return excelFormat;
        }

        return ExcelFormat.xlsx; // Default to xlsx
    }

    private string GetContentType(ExcelFormat format)
    {
        return format switch
        {
            ExcelFormat.xlsx => ExcelFormatContentType.Xlsx,
            ExcelFormat.xls => ExcelFormatContentType.Xls,
            ExcelFormat.csv => ExcelFormatContentType.Csv,
            _ => ExcelFormatContentType.Xlsx
        };
    }

    private string GenerateFileName(DateTime fromDate, string extension)
    {
        return $"{MassBalanceExcelConstants.MassBalanceFilePrefix}{fromDate:yyyyMM}.{extension}";
    }

    // Data retrieval methods (reused from GetMassBalanceHandler)
    private async Task<IEnumerable<Distribution>> GetDistributions(ExportMassBalanceRequest request, CancellationToken cancellationToken)
    {
        var distributions = await _unitOfWork
            .DistributionRepository
            .GetAllAsync(isPaginated: false,
                predicate: d => d.Year == request.FromDate.Year
                                && d.Month == request.FromDate.Month,
                useCache: false,
                cancellationToken: cancellationToken);

        if (distributions.TotalRecords == 0)
            throw new OrionException(
                _unitOfWork.ErrorService.GenerateError(new DistributionsNotFound()));

        return distributions.Results;
    }

    private async Task<IEnumerable<ReportFormat14>> GetReportFormat14(ExportMassBalanceRequest request, CancellationToken cancellationToken)
    {
        var reportFormat14 = await _unitOfWork
            .ReportsFormat14
            .GetAllAsync(isPaginated: false,
                predicate: r => r.VehicleArrival >= DateOnly.FromDateTime(request.FromDate)
                                && r.VehicleArrival < DateOnly.FromDateTime(request.ToDate),
                useCache: false,
                cancellationToken: cancellationToken);

        var tolls = (await _unitOfWork
            .Tolls
            .GetAllAsync(
                isPaginated: false,
                cancellationToken: cancellationToken)).Results;

        var processedReport = ReportFormat14Factory
            .Create(reportFormat14.Results, tolls, DateOnly.FromDateTime(request.FromDate))
            .GetReportFormat14();

        if (!processedReport.Any())
            throw new OrionException(
                _unitOfWork.ErrorService.GenerateError(new ReportFormat14NotFound()));

        return processedReport;
    }

    private async Task<IEnumerable<ReportFormat34>> GetReportFormat34(ExportMassBalanceRequest request, CancellationToken cancellationToken)
    {
        var reportFormat34 = await _unitOfWork
            .ReportsFormat34
            .GetFilteredReportAsync(
                predicate: r => r.FilteredDate >= DateOnly.FromDateTime(request.FromDate)
                           && r.FilteredDate < DateOnly.FromDateTime(request.ToDate)
                           && r.Tons > 0,
                isPaginated: false,
                sortingOptions: new SortingOptions(SortDirection.Ascending, "FilteredDate"),
                cancellationToken: cancellationToken);

        if (!reportFormat34.Results.Any())
            throw new OrionException(
                _unitOfWork.ErrorService.GenerateError(new ReportFormat34NotFound()));

        return reportFormat34.Results;
    }

    private async Task<IEnumerable<RecyclingArea>> GetRecyclingAreas(CancellationToken cancellationToken)
    {
        var recyclingAreas = await _unitOfWork
            .RecyclingAreaRepository
            .GetAllAsync(isPaginated: false,
                useCache: true,
                cacheExpirationInMinutes: MinutesTillCacheInvalidation,
                cancellationToken: cancellationToken);

        if (recyclingAreas.TotalRecords == 0)
            throw new OrionException(
                _unitOfWork.ErrorService.GenerateError(new RecyclingAreaNotFound()));

        return recyclingAreas.Results;
    }

    private async Task<IEnumerable<WeighingScale>> GetWeighins(ExportMassBalanceRequest request, CancellationToken cancellationToken)
    {
        var weighins = await _unitOfWork
            .WeighingScales
            .GetAllAsync(includes: w => w.Town,
                isPaginated: false,
                predicate: r => r.EntryDate >= request.FromDate
                                     && r.EntryDate < request.ToDate
                                     && r.CancelDate == null,
                useCache: false,
                cancellationToken: cancellationToken);

        if (weighins.TotalRecords == 0)
            throw new OrionException(
                _unitOfWork.ErrorService.GenerateError(new WeighingScaleNotFound()));

        return weighins.Results;
    }
}
